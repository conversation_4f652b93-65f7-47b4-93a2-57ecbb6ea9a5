from datetime import datetime, date, time
from flask import jsonify, current_app
from sqlalchemy import and_, or_
from ..main import db
from ..Models.Reservation.ReservationModel import Reservation, ReservationStatus
from ..Models.Tables.TableModel import Table
from ..Utils.validators import ReservationValidator

class ReservationController:
    
    @staticmethod
    def create_reservation(data):
        """Create a new reservation"""
        try:
            # Validate reservation data
            is_valid, errors = ReservationValidator.validate_reservation_data(data)
            if not is_valid:
                return {'error': 'Validation failed', 'details': errors}, 400

            # Parse date and time
            try:
                reservation_date = datetime.strptime(data['reservation_date'], '%Y-%m-%d').date()
                reservation_time = datetime.strptime(data['reservation_time'], '%H:%M').time()
            except ValueError as e:
                return {'error': 'Invalid date or time format'}, 400
            
            # Find available table
            table = ReservationController._find_available_table(
                reservation_date, 
                reservation_time, 
                data['number_of_people']
            )
            
            if not table:
                return {'error': 'No available table for the requested time and party size'}, 400
            
            # Create reservation
            reservation = Reservation(
                user_id=data.get('user_id'),
                customer_name=data['customer_name'],
                email=data.get('email'),
                phone=data['phone'],
                reservation_date=reservation_date,
                reservation_time=reservation_time,
                number_of_people=data['number_of_people'],
                notes=data.get('notes'),
                table_id=table.id,
                status=ReservationStatus.PENDING
            )
            
            db.session.add(reservation)
            db.session.commit()
            
            return {'message': 'Reservation created successfully', 'reservation': reservation.to_dict()}, 201
            
        except Exception as e:
            db.session.rollback()
            return {'error': f'Failed to create reservation: {str(e)}'}, 500
    
    @staticmethod
    def get_reservations(filters=None):
        """Get reservations with optional filters"""
        try:
            query = Reservation.query
            
            if filters:
                if 'date' in filters:
                    query = query.filter(Reservation.reservation_date == filters['date'])
                if 'status' in filters:
                    query = query.filter(Reservation.status == filters['status'])
                if 'user_id' in filters:
                    query = query.filter(Reservation.user_id == filters['user_id'])
            
            reservations = query.order_by(Reservation.reservation_date.desc(), 
                                        Reservation.reservation_time.desc()).all()
            
            return {'reservations': [r.to_dict() for r in reservations]}, 200
            
        except Exception as e:
            return {'error': f'Failed to fetch reservations: {str(e)}'}, 500
    
    @staticmethod
    def get_reservation(reservation_id):
        """Get a specific reservation"""
        try:
            reservation = Reservation.query.get(reservation_id)
            if not reservation:
                return {'error': 'Reservation not found'}, 404
            
            return {'reservation': reservation.to_dict()}, 200
            
        except Exception as e:
            return {'error': f'Failed to fetch reservation: {str(e)}'}, 500
    
    @staticmethod
    def update_reservation(reservation_id, data):
        """Update a reservation"""
        try:
            reservation = Reservation.query.get(reservation_id)
            if not reservation:
                return {'error': 'Reservation not found'}, 404
            
            # Update fields if provided
            if 'customer_name' in data:
                reservation.customer_name = data['customer_name']
            if 'email' in data:
                reservation.email = data['email']
            if 'phone' in data:
                reservation.phone = data['phone']
            if 'notes' in data:
                reservation.notes = data['notes']
            if 'status' in data:
                reservation.status = data['status']
            
            # Handle date/time changes
            if 'reservation_date' in data or 'reservation_time' in data:
                new_date = datetime.strptime(data['reservation_date'], '%Y-%m-%d').date() if 'reservation_date' in data else reservation.reservation_date
                new_time = datetime.strptime(data['reservation_time'], '%H:%M').time() if 'reservation_time' in data else reservation.reservation_time
                
                # Check availability for new time
                if new_date != reservation.reservation_date or new_time != reservation.reservation_time:
                    table = ReservationController._find_available_table(
                        new_date, 
                        new_time, 
                        data.get('number_of_people', reservation.number_of_people),
                        exclude_reservation_id=reservation_id
                    )
                    
                    if not table:
                        return {'error': 'No available table for the new time'}, 400
                    
                    reservation.reservation_date = new_date
                    reservation.reservation_time = new_time
                    reservation.table_id = table.id
            
            if 'number_of_people' in data:
                reservation.number_of_people = data['number_of_people']
            
            reservation.updated_at = datetime.utcnow()
            db.session.commit()
            
            return {'message': 'Reservation updated successfully', 'reservation': reservation.to_dict()}, 200
            
        except Exception as e:
            db.session.rollback()
            return {'error': f'Failed to update reservation: {str(e)}'}, 500
    
    @staticmethod
    def cancel_reservation(reservation_id):
        """Cancel a reservation"""
        try:
            reservation = Reservation.query.get(reservation_id)
            if not reservation:
                return {'error': 'Reservation not found'}, 404
            
            reservation.status = ReservationStatus.CANCELLED
            reservation.updated_at = datetime.utcnow()
            db.session.commit()
            
            return {'message': 'Reservation cancelled successfully'}, 200
            
        except Exception as e:
            db.session.rollback()
            return {'error': f'Failed to cancel reservation: {str(e)}'}, 500
    
    @staticmethod
    def check_availability(reservation_date, reservation_time, number_of_people):
        """Check table availability for given parameters"""
        try:
            # Parse date and time
            try:
                res_date = datetime.strptime(reservation_date, '%Y-%m-%d').date()
                res_time = datetime.strptime(reservation_time, '%H:%M').time()
            except ValueError:
                return {'error': 'Invalid date or time format'}, 400
            
            available_tables = ReservationController._get_available_tables(res_date, res_time, number_of_people)
            
            return {
                'available': len(available_tables) > 0,
                'available_tables': [table.to_dict() for table in available_tables]
            }, 200
            
        except Exception as e:
            return {'error': f'Failed to check availability: {str(e)}'}, 500
    
    @staticmethod
    def _find_available_table(reservation_date, reservation_time, number_of_people, exclude_reservation_id=None):
        """Find an available table for the given parameters"""
        available_tables = ReservationController._get_available_tables(
            reservation_date, 
            reservation_time, 
            number_of_people, 
            exclude_reservation_id
        )
        
        # Return the first available table (you could implement more sophisticated logic here)
        return available_tables[0] if available_tables else None
    
    @staticmethod
    def _get_available_tables(reservation_date, reservation_time, number_of_people, exclude_reservation_id=None):
        """Get all available tables for the given parameters"""
        # Find tables with sufficient capacity
        suitable_tables = Table.query.filter(
            Table.capacity >= number_of_people,
            Table.is_available == True
        ).all()
        
        available_tables = []
        
        for table in suitable_tables:
            # Check if table is available at the requested time
            # Consider a 2-hour window for each reservation
            start_time = (datetime.combine(reservation_date, reservation_time) - 
                         datetime.combine(reservation_date, datetime.min.time())).total_seconds() / 3600
            end_time = start_time + 2  # 2-hour reservation window
            
            conflicting_reservations = Reservation.query.filter(
                Reservation.table_id == table.id,
                Reservation.reservation_date == reservation_date,
                Reservation.status.in_([ReservationStatus.PENDING, ReservationStatus.CONFIRMED]),
                # Check for time overlap
                or_(
                    and_(
                        db.func.extract('hour', Reservation.reservation_time) + 
                        db.func.extract('minute', Reservation.reservation_time) / 60 >= start_time - 2,
                        db.func.extract('hour', Reservation.reservation_time) + 
                        db.func.extract('minute', Reservation.reservation_time) / 60 < end_time
                    ),
                    and_(
                        db.func.extract('hour', Reservation.reservation_time) + 
                        db.func.extract('minute', Reservation.reservation_time) / 60 + 2 > start_time,
                        db.func.extract('hour', Reservation.reservation_time) + 
                        db.func.extract('minute', Reservation.reservation_time) / 60 <= start_time
                    )
                )
            )
            
            if exclude_reservation_id:
                conflicting_reservations = conflicting_reservations.filter(
                    Reservation.id != exclude_reservation_id
                )
            
            if conflicting_reservations.count() == 0:
                available_tables.append(table)
        
        return available_tables
