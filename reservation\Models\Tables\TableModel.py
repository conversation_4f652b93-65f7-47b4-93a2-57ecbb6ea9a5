from datetime import datetime
from ...main import db

class Table(db.Model):
    __tablename__ = 'tables'

    id = db.Column(db.Integer, primary_key=True)
    table_number = db.Column(db.String(10), unique=True, nullable=False)
    capacity = db.Column(db.Integer, nullable=False)
    location = db.Column(db.String(50), nullable=True)  # e.g., 'window', 'center', 'patio'
    is_available = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship with reservations
    reservations = db.relationship('Reservation', backref='table', lazy=True)

    def __repr__(self):
        return f'<Table {self.table_number} (capacity: {self.capacity})>'

    def to_dict(self):
        return {
            'id': self.id,
            'table_number': self.table_number,
            'capacity': self.capacity,
            'location': self.location,
            'is_available': self.is_available,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }