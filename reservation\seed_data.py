from main import create_app, db
from Models.Tables.TableModel import Table

def seed_tables():
    """Seed the database with initial table data"""
    app = create_app()
    
    with app.app_context():
        # Check if tables already exist
        if Table.query.count() > 0:
            print("Tables already exist. Skipping seed.")
            return
        
        # Create sample tables
        tables_data = [
            {'table_number': 'T01', 'capacity': 2, 'location': 'window'},
            {'table_number': 'T02', 'capacity': 2, 'location': 'window'},
            {'table_number': 'T03', 'capacity': 4, 'location': 'center'},
            {'table_number': 'T04', 'capacity': 4, 'location': 'center'},
            {'table_number': 'T05', 'capacity': 6, 'location': 'center'},
            {'table_number': 'T06', 'capacity': 6, 'location': 'center'},
            {'table_number': 'T07', 'capacity': 8, 'location': 'center'},
            {'table_number': 'T08', 'capacity': 2, 'location': 'patio'},
            {'table_number': 'T09', 'capacity': 4, 'location': 'patio'},
            {'table_number': 'T10', 'capacity': 4, 'location': 'patio'},
            {'table_number': 'T11', 'capacity': 6, 'location': 'patio'},
            {'table_number': 'T12', 'capacity': 8, 'location': 'patio'},
        ]
        
        for table_data in tables_data:
            table = Table(
                table_number=table_data['table_number'],
                capacity=table_data['capacity'],
                location=table_data['location'],
                is_available=True
            )
            db.session.add(table)
        
        db.session.commit()
        print(f"Successfully seeded {len(tables_data)} tables.")

if __name__ == '__main__':
    seed_tables()
