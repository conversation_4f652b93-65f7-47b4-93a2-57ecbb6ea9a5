import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    SECRET_KEY = os.environ.get('SECRET_KEY', 'your-secret-key-here-change-in-production')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # CORS settings
    CORS_ORIGINS = [
        "http://localhost:3000",  # React development server
        "http://localhost:5173",  # Vite development server
        "http://localhost:8080",  # Vue development server
    ]
    
    # Reservation settings
    RESERVATION_DURATION_HOURS = 2  # Default reservation duration
    MAX_ADVANCE_BOOKING_DAYS = 30   # Maximum days in advance for booking
    MIN_ADVANCE_BOOKING_HOURS = 1   # Minimum hours in advance for booking
    
    # Restaurant operating hours
    RESTAURANT_OPEN_TIME = "10:00"
    RESTAURANT_CLOSE_TIME = "22:00"
    
    # Business rules
    MAX_PARTY_SIZE = 12
    MIN_PARTY_SIZE = 1

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///reservations_dev.db')

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///reservations.db')

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
