# Reservation Microservice API Documentation

## Base URL
```
http://localhost:5001
```

## Health Check
```
GET /
```
Returns service status and version information.

## Reservation Endpoints

### Create Reservation
```
POST /api/reservations/
```

**Request Body:**
```json
{
  "user_id": 123,                    // Optional: Link to main backend user
  "customer_name": "<PERSON>",       // Required
  "email": "<EMAIL>",       // Optional
  "phone": "+**********",           // Required
  "reservation_date": "2024-07-15", // Required (YYYY-MM-DD)
  "reservation_time": "19:30",      // Required (HH:MM)
  "number_of_people": 4,            // Required
  "notes": "Birthday celebration"    // Optional
}
```

**Response:**
```json
{
  "message": "Reservation created successfully",
  "reservation": {
    "id": 1,
    "user_id": 123,
    "customer_name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+**********",
    "reservation_date": "2024-07-15",
    "reservation_time": "19:30:00",
    "number_of_people": 4,
    "notes": "Birthday celebration",
    "status": "pending",
    "table_id": 5,
    "table": {
      "id": 5,
      "table_number": "T05",
      "capacity": 6,
      "location": "center"
    },
    "created_at": "2024-07-14T10:30:00",
    "updated_at": "2024-07-14T10:30:00"
  }
}
```

### Get All Reservations
```
GET /api/reservations/
```

**Query Parameters:**
- `date`: Filter by reservation date (YYYY-MM-DD)
- `status`: Filter by status (pending, confirmed, cancelled, completed)
- `user_id`: Filter by user ID

### Get Specific Reservation
```
GET /api/reservations/{reservation_id}
```

### Update Reservation
```
PUT /api/reservations/{reservation_id}
```

**Request Body:** Same as create, all fields optional

### Cancel Reservation
```
PUT /api/reservations/{reservation_id}/cancel
```

### Check Availability
```
GET /api/reservations/check-availability?date=2024-07-15&time=19:30&people=4
```

**Response:**
```json
{
  "available": true,
  "available_tables": [
    {
      "id": 5,
      "table_number": "T05",
      "capacity": 6,
      "location": "center",
      "is_available": true
    }
  ]
}
```

### Get User Reservations
```
GET /api/reservations/user/{user_id}
```

**Query Parameters:**
- `status`: Filter by status

## Table Management Endpoints

### Create Table
```
POST /api/tables/
```

**Request Body:**
```json
{
  "table_number": "T13",           // Required, unique
  "capacity": 4,                   // Required (1-20)
  "location": "patio",            // Optional (window, center, patio, bar, private)
  "is_available": true            // Optional, default: true
}
```

### Get All Tables
```
GET /api/tables/
```

### Get Specific Table
```
GET /api/tables/{table_id}
```

### Update Table
```
PUT /api/tables/{table_id}
```

### Delete Table
```
DELETE /api/tables/{table_id}
```

### Get Available Tables
```
GET /api/tables/available
```

**Query Parameters:**
- `capacity`: Minimum capacity required
- `location`: Filter by location

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

```json
{
  "error": "Error description",
  "details": ["Specific error 1", "Specific error 2"]  // For validation errors
}
```

## Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `404`: Not Found
- `500`: Internal Server Error

## Business Rules

1. **Reservation Time Validation:**
   - Must be at least 1 hour in advance
   - Cannot be more than 30 days in advance
   - Must be within restaurant hours (10:00 - 22:00)

2. **Party Size:**
   - Minimum: 1 person
   - Maximum: 12 people

3. **Table Assignment:**
   - Automatic assignment to smallest suitable table
   - 2-hour reservation window
   - No overlapping reservations

4. **Reservation Status:**
   - `pending`: Initial status
   - `confirmed`: Confirmed by restaurant
   - `cancelled`: Cancelled by customer or restaurant
   - `completed`: Reservation completed

## Integration with Main Backend

The microservice can be integrated with your main FastAPI backend by:

1. **User Linking:** Include `user_id` in reservation requests
2. **Authentication:** Add JWT token validation (optional)
3. **API Gateway:** Route reservation requests through your main API
4. **Database Sync:** Optionally sync user data between services
