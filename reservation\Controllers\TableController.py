from flask import jsonify
from ..main import db
from ..Models.Tables.TableModel import Table
from ..Utils.validators import TableValidator

class TableController:
    
    @staticmethod
    def create_table(data):
        """Create a new table"""
        try:
            # Validate table data
            is_valid, errors = TableValidator.validate_table_data(data)
            if not is_valid:
                return {'error': 'Validation failed', 'details': errors}, 400
            
            # Check if table number already exists
            existing_table = Table.query.filter_by(table_number=data['table_number']).first()
            if existing_table:
                return {'error': 'Table number already exists'}, 400
            
            # Create table
            table = Table(
                table_number=data['table_number'],
                capacity=data['capacity'],
                location=data.get('location'),
                is_available=data.get('is_available', True)
            )
            
            db.session.add(table)
            db.session.commit()
            
            return {'message': 'Table created successfully', 'table': table.to_dict()}, 201
            
        except Exception as e:
            db.session.rollback()
            return {'error': f'Failed to create table: {str(e)}'}, 500
    
    @staticmethod
    def get_tables():
        """Get all tables"""
        try:
            tables = Table.query.order_by(Table.table_number).all()
            return {'tables': [table.to_dict() for table in tables]}, 200
            
        except Exception as e:
            return {'error': f'Failed to fetch tables: {str(e)}'}, 500
    
    @staticmethod
    def get_table(table_id):
        """Get a specific table"""
        try:
            table = Table.query.get(table_id)
            if not table:
                return {'error': 'Table not found'}, 404
            
            return {'table': table.to_dict()}, 200
            
        except Exception as e:
            return {'error': f'Failed to fetch table: {str(e)}'}, 500
    
    @staticmethod
    def update_table(table_id, data):
        """Update a table"""
        try:
            table = Table.query.get(table_id)
            if not table:
                return {'error': 'Table not found'}, 404
            
            # Check if new table number conflicts with existing ones
            if 'table_number' in data and data['table_number'] != table.table_number:
                existing_table = Table.query.filter_by(table_number=data['table_number']).first()
                if existing_table:
                    return {'error': 'Table number already exists'}, 400
            
            # Update fields if provided
            if 'table_number' in data:
                table.table_number = data['table_number']
            if 'capacity' in data:
                table.capacity = data['capacity']
            if 'location' in data:
                table.location = data['location']
            if 'is_available' in data:
                table.is_available = data['is_available']
            
            db.session.commit()
            
            return {'message': 'Table updated successfully', 'table': table.to_dict()}, 200
            
        except Exception as e:
            db.session.rollback()
            return {'error': f'Failed to update table: {str(e)}'}, 500
    
    @staticmethod
    def delete_table(table_id):
        """Delete a table"""
        try:
            table = Table.query.get(table_id)
            if not table:
                return {'error': 'Table not found'}, 404
            
            # Check if table has active reservations
            from ..Models.Reservation.ReservationModel import Reservation, ReservationStatus
            active_reservations = Reservation.query.filter(
                Reservation.table_id == table_id,
                Reservation.status.in_([ReservationStatus.PENDING, ReservationStatus.CONFIRMED])
            ).count()
            
            if active_reservations > 0:
                return {'error': 'Cannot delete table with active reservations'}, 400
            
            db.session.delete(table)
            db.session.commit()
            
            return {'message': 'Table deleted successfully'}, 200
            
        except Exception as e:
            db.session.rollback()
            return {'error': f'Failed to delete table: {str(e)}'}, 500
    
    @staticmethod
    def get_available_tables(filters=None):
        """Get available tables with optional filters"""
        try:
            query = Table.query.filter(Table.is_available == True)
            
            if filters:
                if 'capacity' in filters:
                    query = query.filter(Table.capacity >= filters['capacity'])
                if 'location' in filters:
                    query = query.filter(Table.location == filters['location'])
            
            tables = query.order_by(Table.table_number).all()
            return {'tables': [table.to_dict() for table in tables]}, 200
            
        except Exception as e:
            return {'error': f'Failed to fetch available tables: {str(e)}'}, 500
