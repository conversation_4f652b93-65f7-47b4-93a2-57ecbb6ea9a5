import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8000', // Adjust this to match your backend URL
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types for API requests and responses
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  is_active: boolean;
  role: string;
  created_at: string;
}

// Authentication API functions
export const authAPI = {
  // Login user
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post('/users/login', credentials);
    return response.data;
  },

  // Register user
  register: async (userData: RegisterRequest): Promise<User> => {
    const response = await api.post('/users/register', userData);
    return response.data;
  },

  // Get current user profile
  getProfile: async (): Promise<User> => {
    const response = await api.get('/users/me');
    return response.data;
  },

  // Update user profile
  updateProfile: async (userId: number, userData: Partial<RegisterRequest>): Promise<User> => {
    const response = await api.put(`/users/${userId}`, userData);
    return response.data;
  },
};

// Menu API functions
export const menuAPI = {
  // Get all menu items
  getMenuItems: async () => {
    const response = await api.get('/menus');
    return response.data;
  },

  // Get menu item by ID
  getMenuItem: async (id: string) => {
    const response = await api.get(`/menus/${id}`);
    return response.data;
  },

  // Get categories
  getCategories: async () => {
    const response = await api.get('/categories');
    return response.data;
  },
};

// Define an interface for OrderData
export interface OrderData {
  // Add properties according to your backend requirements
  items: Array<{
    menu_item_id: number;
    quantity: number;
  }>;
  total_price: number;
  reservation_id?: number;
  notes?: string;
}

// Order API functions
export const orderAPI = {
  // Create new order
  createOrder: async (orderData: OrderData) => {
    const response = await api.post('/orders', orderData);
    return response.data;
  },

  // Get user orders
  getUserOrders: async () => {
    const response = await api.get('/orders');
    return response.data;
  },

  // Get order by ID
  getOrder: async (id: string) => {
    const response = await api.get(`/orders/${id}`);
    return response.data;
  },
};

// Define an interface for ReservationData
export interface ReservationData {
  // Add properties according to your backend requirements
  date: string;
  time: string;
  number_of_people: number;
  notes?: string;
  // Add more fields as needed
}

// Reservation API functions
export const reservationAPI = {
  // Create reservation
  createReservation: async (reservationData: ReservationData) => {
    const response = await api.post('/reservations', reservationData);
    return response.data;
  },

  // Get user reservations
  getUserReservations: async () => {
    const response = await api.get('/reservations');
    return response.data;
  },
};

export default api;
