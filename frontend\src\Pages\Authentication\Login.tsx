import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Label, TextInput, Alert, Spinner } from 'flowbite-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppStore } from '../../store/store';
import { authAPI } from '../../services/api';
import type { LoginRequest } from '../../services/api';


const Login = () => {
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  const navigate = useNavigate();
  const { login, setError: setStoreError } = useAppStore();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Call login API
      const response = await authAPI.login(formData);

      // Store token in localStorage
      localStorage.setItem('access_token', response.access_token);

      // Get user profile to store in state
      try {
        const userProfile = await authAPI.getProfile();

        // Update store with user data
        login({
          id: userProfile.id.toString(),
          name: `${userProfile.first_name} ${userProfile.last_name}`,
          email: userProfile.email,
          role: userProfile.role,
        });

        // Navigate to home page
        navigate('/');
      } catch (profileError) {
        // If profile fetch fails, still log in but with basic info
        console.error('Failed to fetch user profile:', profileError);
        login({
          id: '1',
          name: formData.username,
          email: '',
          role: 'customer',
        });
        navigate('/');
      }
    } catch (err: unknown) {
      let errorMessage = 'Login failed. Please try again.';
      interface ErrorWithResponse {
        response?: {
          data?: {
            detail?: string;
          };
        };
      }
      if (
        err &&
        typeof err === 'object' &&
        'response' in err &&
        (err as ErrorWithResponse).response?.data?.detail
      ) {
        errorMessage = (err as ErrorWithResponse).response!.data!.detail!;
      }
      setError(errorMessage);
      setStoreError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center py-12 px-4">
      {/* Background Animation Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 animate-bounce delay-1000">
          <div className="text-6xl opacity-10">🥙</div>
        </div>
        <div className="absolute top-40 right-20 animate-bounce delay-2000">
          <div className="text-4xl opacity-10">🍢</div>
        </div>
        <div className="absolute bottom-20 left-1/4 animate-bounce delay-3000">
          <div className="text-5xl opacity-10">🥗</div>
        </div>
        <div className="absolute bottom-40 right-1/3 animate-bounce delay-4000">
          <div className="text-4xl opacity-10">🧃</div>
        </div>
      </div>

      <div className="w-full max-w-md relative z-10">
        <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          {/* Logo/Brand Section */}
          <div className="text-center mb-8">
            <div className="text-6xl mb-4 animate-pulse">🍽️</div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-600 bg-clip-text text-transparent mb-2">
              Taste of Turkey
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Welcome back! Please sign in to your account
            </p>
          </div>

          {/* Login Card */}
          <Card className="shadow-2xl border-0 backdrop-blur-sm bg-white/90 dark:bg-gray-800/90 hover:shadow-3xl transition-all duration-300">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                  Sign In
                </h2>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Enter your credentials to access your account
                </p>
              </div>

              {/* Error Alert */}
              {error && (
                <Alert color="failure" className="animate-shake">
                  <span className="font-medium">Error!</span> {error}
                </Alert>
              )}

              {/* Username Field */}
              <div className="space-y-2">
                <Label htmlFor="username" className="text-gray-700 dark:text-gray-300">Username</Label>
                <TextInput
                  id="username"
                  name="username"
                  type="text"
                  placeholder="Enter your username"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="transform transition-all duration-300 focus:scale-105"
                  icon={() => <span className="text-orange-500">👤</span>}
                />
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-700 dark:text-gray-300">Password</Label>
                <TextInput
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter your password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="transform transition-all duration-300 focus:scale-105"
                  icon={() => <span className="text-orange-500">🔒</span>}
                />
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading || !formData.username || !formData.password}
                className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
                size="lg"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Spinner size="sm" />
                    <span>Signing In...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <span>🚀</span>
                    <span>Sign In</span>
                  </div>
                )}
              </Button>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white dark:bg-gray-800 text-gray-500">
                    Don't have an account?
                  </span>
                </div>
              </div>

              {/* Register Link */}
              <div className="text-center">
                <Link
                  to="/register"
                  className="text-orange-500 hover:text-orange-600 font-medium hover:underline transition-all duration-300 transform hover:scale-105 inline-block"
                >
                  Create a new account →
                </Link>
              </div>

              {/* Additional Links */}
              <div className="text-center space-y-2">
                <Link
                  to="/forgot-password"
                  className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:underline transition-colors duration-300"
                >
                  Forgot your password?
                </Link>
              </div>
            </form>
          </Card>

          {/* Features */}
          <div className="mt-8 text-center">
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
              Join thousands of satisfied customers
            </p>
            <div className="flex justify-center space-x-6 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <span>🚚</span>
                <span>Fast Delivery</span>
              </div>
              <div className="flex items-center gap-1">
                <span>📅</span>
                <span>Easy Reservations</span>
              </div>
              <div className="flex items-center gap-1">
                <span>⭐</span>
                <span>5-Star Service</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;