from datetime import datetime
from ...main import db

class ReservationStatus:
    PENDING = 'pending'
    CONFIRMED = 'confirmed'
    CANCELLED = 'cancelled'
    COMPLETED = 'completed'

class Reservation(db.Model):
    __tablename__ = 'reservations'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=True)  # Link to main backend user system
    customer_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=True)
    phone = db.Column(db.String(20), nullable=False)
    reservation_date = db.Column(db.Date, nullable=False)
    reservation_time = db.Column(db.Time, nullable=False)
    number_of_people = db.Column(db.Integer, nullable=False)
    notes = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(20), default=ReservationStatus.PENDING)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Foreign key to table
    table_id = db.Column(db.Integer, db.ForeignKey('tables.id'), nullable=True)

    def __repr__(self):
        return f'<Reservation {self.id} - {self.customer_name} on {self.reservation_date}>'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'customer_name': self.customer_name,
            'email': self.email,
            'phone': self.phone,
            'reservation_date': self.reservation_date.isoformat() if self.reservation_date else None,
            'reservation_time': self.reservation_time.isoformat() if self.reservation_time else None,
            'number_of_people': self.number_of_people,
            'notes': self.notes,
            'status': self.status,
            'table_id': self.table_id,
            'table': self.table.to_dict() if self.table else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }