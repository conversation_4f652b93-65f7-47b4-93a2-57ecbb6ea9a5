from datetime import datetime, date, time, timedelta
from flask import current_app
import re

class ReservationValidator:
    """Utility class for validating reservation data"""
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        if not email:
            return True  # Email is optional
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone):
        """Validate phone number format"""
        if not phone:
            return False
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        
        # Check if it has 10-15 digits (international format)
        return 10 <= len(digits_only) <= 15
    
    @staticmethod
    def validate_party_size(number_of_people):
        """Validate party size"""
        try:
            party_size = int(number_of_people)
            min_size = current_app.config.get('MIN_PARTY_SIZE', 1)
            max_size = current_app.config.get('MAX_PARTY_SIZE', 12)
            return min_size <= party_size <= max_size
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_reservation_time(reservation_date, reservation_time):
        """Validate reservation date and time"""
        try:
            # Parse date and time
            if isinstance(reservation_date, str):
                res_date = datetime.strptime(reservation_date, '%Y-%m-%d').date()
            else:
                res_date = reservation_date
                
            if isinstance(reservation_time, str):
                res_time = datetime.strptime(reservation_time, '%H:%M').time()
            else:
                res_time = reservation_time
            
            # Combine date and time
            reservation_datetime = datetime.combine(res_date, res_time)
            
            # Check if reservation is in the past
            min_advance_hours = current_app.config.get('MIN_ADVANCE_BOOKING_HOURS', 1)
            min_booking_time = datetime.now() + timedelta(hours=min_advance_hours)
            
            if reservation_datetime <= min_booking_time:
                return False, f"Reservation must be at least {min_advance_hours} hour(s) in advance"
            
            # Check if reservation is too far in the future
            max_advance_days = current_app.config.get('MAX_ADVANCE_BOOKING_DAYS', 30)
            max_booking_time = datetime.now() + timedelta(days=max_advance_days)
            
            if reservation_datetime > max_booking_time:
                return False, f"Reservation cannot be more than {max_advance_days} days in advance"
            
            # Check if reservation is within operating hours
            open_time_str = current_app.config.get('RESTAURANT_OPEN_TIME', '10:00')
            close_time_str = current_app.config.get('RESTAURANT_CLOSE_TIME', '22:00')
            
            open_time = datetime.strptime(open_time_str, '%H:%M').time()
            close_time = datetime.strptime(close_time_str, '%H:%M').time()
            
            if not (open_time <= res_time <= close_time):
                return False, f"Reservation must be between {open_time_str} and {close_time_str}"
            
            return True, "Valid reservation time"
            
        except ValueError as e:
            return False, f"Invalid date or time format: {str(e)}"
    
    @staticmethod
    def validate_reservation_data(data):
        """Validate complete reservation data"""
        errors = []
        
        # Required fields
        required_fields = ['customer_name', 'phone', 'reservation_date', 'reservation_time', 'number_of_people']
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f'{field} is required')
        
        if errors:
            return False, errors
        
        # Validate email if provided
        if 'email' in data and data['email']:
            if not ReservationValidator.validate_email(data['email']):
                errors.append('Invalid email format')
        
        # Validate phone
        if not ReservationValidator.validate_phone(data['phone']):
            errors.append('Invalid phone number format')
        
        # Validate party size
        if not ReservationValidator.validate_party_size(data['number_of_people']):
            min_size = current_app.config.get('MIN_PARTY_SIZE', 1)
            max_size = current_app.config.get('MAX_PARTY_SIZE', 12)
            errors.append(f'Party size must be between {min_size} and {max_size}')
        
        # Validate reservation time
        is_valid_time, time_message = ReservationValidator.validate_reservation_time(
            data['reservation_date'], 
            data['reservation_time']
        )
        if not is_valid_time:
            errors.append(time_message)
        
        # Validate customer name
        if len(data['customer_name'].strip()) < 2:
            errors.append('Customer name must be at least 2 characters long')
        
        return len(errors) == 0, errors

class TableValidator:
    """Utility class for validating table data"""
    
    @staticmethod
    def validate_table_number(table_number):
        """Validate table number format"""
        if not table_number:
            return False
        
        # Table number should be alphanumeric and 1-10 characters
        return re.match(r'^[A-Za-z0-9]{1,10}$', table_number.strip()) is not None
    
    @staticmethod
    def validate_capacity(capacity):
        """Validate table capacity"""
        try:
            cap = int(capacity)
            return 1 <= cap <= 20  # Reasonable range for table capacity
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_location(location):
        """Validate table location"""
        if not location:
            return True  # Location is optional
        
        valid_locations = ['window', 'center', 'patio', 'bar', 'private']
        return location.lower() in valid_locations
    
    @staticmethod
    def validate_table_data(data):
        """Validate complete table data"""
        errors = []
        
        # Required fields
        required_fields = ['table_number', 'capacity']
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f'{field} is required')
        
        if errors:
            return False, errors
        
        # Validate table number
        if not TableValidator.validate_table_number(data['table_number']):
            errors.append('Table number must be alphanumeric and 1-10 characters long')
        
        # Validate capacity
        if not TableValidator.validate_capacity(data['capacity']):
            errors.append('Table capacity must be between 1 and 20')
        
        # Validate location if provided
        if 'location' in data and data['location']:
            if not TableValidator.validate_location(data['location']):
                errors.append('Invalid location. Valid options: window, center, patio, bar, private')
        
        return len(errors) == 0, errors
