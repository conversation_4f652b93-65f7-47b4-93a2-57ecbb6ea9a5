# Restaurant Reservation Microservice

A Flask-based microservice for managing restaurant table reservations with SQLAlchemy and SQLite.

## Features

- **Table Management**: Create, update, and manage restaurant tables
- **Reservation System**: Full CRUD operations for reservations
- **Availability Checking**: Real-time table availability verification
- **Business Logic**: Automatic table assignment and conflict prevention
- **Validation**: Comprehensive input validation and business rules
- **User Integration**: Optional integration with main user system
- **RESTful API**: Clean, documented API endpoints

## Project Structure

```
reservation/
├── Controllers/
│   ├── ReservationController.py    # Reservation business logic
│   └── TableController.py          # Table management logic
├── Models/
│   ├── Reservation/
│   │   └── ReservationModel.py     # Reservation data model
│   └── Tables/
│       └── TableModel.py           # Table data model
├── Routes/
│   ├── ReservationRoutes.py        # Reservation API endpoints
│   └── TableRoutes.py              # Table API endpoints
├── Utils/
│   └── validators.py               # Input validation utilities
├── main.py                         # Flask application factory
├── config.py                       # Configuration settings
├── seed_data.py                    # Database seeding script
├── test_api.py                     # API testing script
├── requirements.txt                # Python dependencies
├── API_DOCUMENTATION.md            # Complete API documentation
└── README.md                       # This file
```

## Installation

1. **Navigate to the reservation directory:**
   ```bash
   cd reservation
   ```

2. **Create a virtual environment (recommended):**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Setup

1. **Initialize the database and seed tables:**
   ```bash
   python seed_data.py
   ```

2. **Start the microservice:**
   ```bash
   python main.py
   ```

   The service will start on `http://localhost:5001`

## Configuration

Edit `config.py` to customize:

- **Database settings**: SQLite file location
- **Business rules**: Reservation duration, advance booking limits
- **Restaurant hours**: Operating hours for validation
- **CORS settings**: Allowed origins for frontend integration

## API Usage

### Quick Start Examples

**Check service status:**
```bash
curl http://localhost:5001/
```

**Create a reservation:**
```bash
curl -X POST http://localhost:5001/api/reservations/ \
  -H "Content-Type: application/json" \
  -d '{
    "customer_name": "John Doe",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "reservation_date": "2024-07-15",
    "reservation_time": "19:30",
    "number_of_people": 4,
    "notes": "Birthday celebration"
  }'
```

**Check availability:**
```bash
curl "http://localhost:5001/api/reservations/check-availability?date=2024-07-15&time=19:30&people=4"
```

**Get all reservations:**
```bash
curl http://localhost:5001/api/reservations/
```

## Testing

Run the included test script to verify all endpoints:

```bash
python test_api.py
```

This will test:
- Health check
- Table creation and retrieval
- Reservation creation, updating, and cancellation
- Availability checking

## Integration with Main Backend

### Option 1: Direct Integration
Include `user_id` in reservation requests to link with your FastAPI user system:

```python
# In your FastAPI backend
import requests

def create_reservation_for_user(user_id, reservation_data):
    reservation_data['user_id'] = user_id
    response = requests.post(
        'http://localhost:5001/api/reservations/',
        json=reservation_data
    )
    return response.json()
```

### Option 2: API Gateway Pattern
Route reservation requests through your main FastAPI application:

```python
# In your FastAPI main.py
from fastapi import APIRouter
import httpx

reservation_router = APIRouter(prefix="/reservations")

@reservation_router.post("/")
async def create_reservation(reservation_data: dict):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:5001/api/reservations/",
            json=reservation_data
        )
        return response.json()

app.include_router(reservation_router)
```

## Business Rules

- **Advance Booking**: 1 hour minimum, 30 days maximum
- **Operating Hours**: 10:00 AM - 10:00 PM (configurable)
- **Party Size**: 1-12 people (configurable)
- **Reservation Duration**: 2 hours per reservation
- **Table Assignment**: Automatic assignment to smallest suitable table

## Database Schema

### Tables
- `id`: Primary key
- `table_number`: Unique identifier (e.g., "T01")
- `capacity`: Number of seats
- `location`: Table location (window, center, patio, etc.)
- `is_available`: Availability status

### Reservations
- `id`: Primary key
- `user_id`: Optional link to main user system
- `customer_name`: Customer name
- `email`: Customer email (optional)
- `phone`: Customer phone number
- `reservation_date`: Date of reservation
- `reservation_time`: Time of reservation
- `number_of_people`: Party size
- `notes`: Additional notes
- `status`: pending, confirmed, cancelled, completed
- `table_id`: Foreign key to assigned table
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

## Environment Variables

Set these environment variables for production:

```bash
export FLASK_ENV=production
export SECRET_KEY=your-secret-key-here
export DATABASE_URL=sqlite:///reservations.db
```

## Development

To extend the microservice:

1. **Add new endpoints**: Create routes in the `Routes/` directory
2. **Add business logic**: Implement in `Controllers/` directory
3. **Add validation**: Extend validators in `Utils/validators.py`
4. **Add models**: Create new models in `Models/` directory

## Production Deployment

For production deployment:

1. Use a production WSGI server (e.g., Gunicorn)
2. Configure a production database (PostgreSQL recommended)
3. Set up proper logging and monitoring
4. Use environment variables for sensitive configuration
5. Implement authentication/authorization if needed

```bash
# Example production start
gunicorn -w 4 -b 0.0.0.0:5001 "main:create_app('production')"
```

## API Documentation

See `API_DOCUMENTATION.md` for complete API reference with examples and response formats.
