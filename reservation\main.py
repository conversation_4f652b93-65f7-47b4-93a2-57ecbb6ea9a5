from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import os
from config import config

# Initialize extensions
db = SQLAlchemy()

def create_app(config_name=None):
    # Initialize Flask app
    app = Flask(__name__)

    # Load configuration
    config_name = config_name or os.environ.get('FLASK_ENV', 'development')
    app.config.from_object(config[config_name])

    # Initialize extensions with app
    db.init_app(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])

    # Import models after db initialization
    from Models.Reservation.ReservationModel import Reservation
    from Models.Tables.TableModel import Table

    # Import routes
    from Routes.ReservationRoutes import reservation_bp
    from Routes.TableRoutes import table_bp

    # Register blueprints
    app.register_blueprint(reservation_bp, url_prefix='/api/reservations')
    app.register_blueprint(table_bp, url_prefix='/api/tables')

    # Create tables
    with app.app_context():
        db.create_all()
        print("Database tables created.")

    # Root endpoint
    @app.route('/')
    def health_check():
        return {
            "message": "Reservation Microservice is running",
            "service": "reservations",
            "version": "1.0.0"
        }

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, port=5001)