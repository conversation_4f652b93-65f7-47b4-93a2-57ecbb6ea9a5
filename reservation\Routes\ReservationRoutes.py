from flask import Blueprint, request, jsonify
from ..Controllers.ReservationController import ReservationController

reservation_bp = Blueprint('reservations', __name__)

@reservation_bp.route('/', methods=['POST'])
def create_reservation():
    """Create a new reservation"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    result, status_code = ReservationController.create_reservation(data)
    return jsonify(result), status_code

@reservation_bp.route('/', methods=['GET'])
def get_reservations():
    """Get reservations with optional filters"""
    filters = {}
    
    # Extract query parameters
    if request.args.get('date'):
        filters['date'] = request.args.get('date')
    if request.args.get('status'):
        filters['status'] = request.args.get('status')
    if request.args.get('user_id'):
        filters['user_id'] = int(request.args.get('user_id'))
    
    result, status_code = ReservationController.get_reservations(filters if filters else None)
    return jsonify(result), status_code

@reservation_bp.route('/<int:reservation_id>', methods=['GET'])
def get_reservation(reservation_id):
    """Get a specific reservation"""
    result, status_code = ReservationController.get_reservation(reservation_id)
    return jsonify(result), status_code

@reservation_bp.route('/<int:reservation_id>', methods=['PUT'])
def update_reservation(reservation_id):
    """Update a reservation"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    result, status_code = ReservationController.update_reservation(reservation_id, data)
    return jsonify(result), status_code

@reservation_bp.route('/<int:reservation_id>/cancel', methods=['PUT'])
def cancel_reservation(reservation_id):
    """Cancel a reservation"""
    result, status_code = ReservationController.cancel_reservation(reservation_id)
    return jsonify(result), status_code

@reservation_bp.route('/check-availability', methods=['GET'])
def check_availability():
    """Check table availability"""
    reservation_date = request.args.get('date')
    reservation_time = request.args.get('time')
    number_of_people = request.args.get('people')
    
    if not all([reservation_date, reservation_time, number_of_people]):
        return jsonify({'error': 'date, time, and people parameters are required'}), 400
    
    try:
        number_of_people = int(number_of_people)
    except ValueError:
        return jsonify({'error': 'people must be a valid number'}), 400
    
    result, status_code = ReservationController.check_availability(
        reservation_date, 
        reservation_time, 
        number_of_people
    )
    return jsonify(result), status_code

@reservation_bp.route('/user/<int:user_id>', methods=['GET'])
def get_user_reservations(user_id):
    """Get reservations for a specific user"""
    filters = {'user_id': user_id}
    
    # Add additional filters if provided
    if request.args.get('status'):
        filters['status'] = request.args.get('status')
    
    result, status_code = ReservationController.get_reservations(filters)
    return jsonify(result), status_code
