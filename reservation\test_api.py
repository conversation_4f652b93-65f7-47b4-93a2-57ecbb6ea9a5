import requests
import json
from datetime import datetime, timedelta

# Base URL for the reservation service
BASE_URL = "http://localhost:5001"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    response = requests.get(f"{BASE_URL}/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_create_table():
    """Test creating a table"""
    print("Testing table creation...")
    table_data = {
        "table_number": "TEST01",
        "capacity": 4,
        "location": "center",
        "is_available": True
    }
    
    response = requests.post(f"{BASE_URL}/api/tables/", json=table_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)
    return response.json().get('table', {}).get('id') if response.status_code == 201 else None

def test_get_tables():
    """Test getting all tables"""
    print("Testing get all tables...")
    response = requests.get(f"{BASE_URL}/api/tables/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_create_reservation():
    """Test creating a reservation"""
    print("Testing reservation creation...")
    
    # Create a reservation for tomorrow at 7 PM
    tomorrow = datetime.now() + timedelta(days=1)
    reservation_data = {
        "customer_name": "Test Customer",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "reservation_date": tomorrow.strftime("%Y-%m-%d"),
        "reservation_time": "19:00",
        "number_of_people": 4,
        "notes": "Test reservation"
    }
    
    response = requests.post(f"{BASE_URL}/api/reservations/", json=reservation_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)
    return response.json().get('reservation', {}).get('id') if response.status_code == 201 else None

def test_check_availability():
    """Test checking availability"""
    print("Testing availability check...")
    tomorrow = datetime.now() + timedelta(days=1)
    
    params = {
        "date": tomorrow.strftime("%Y-%m-%d"),
        "time": "20:00",
        "people": "2"
    }
    
    response = requests.get(f"{BASE_URL}/api/reservations/check-availability", params=params)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_get_reservations():
    """Test getting all reservations"""
    print("Testing get all reservations...")
    response = requests.get(f"{BASE_URL}/api/reservations/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_update_reservation(reservation_id):
    """Test updating a reservation"""
    if not reservation_id:
        print("No reservation ID provided, skipping update test")
        return
    
    print(f"Testing reservation update for ID {reservation_id}...")
    update_data = {
        "notes": "Updated test reservation",
        "number_of_people": 6
    }
    
    response = requests.put(f"{BASE_URL}/api/reservations/{reservation_id}", json=update_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_cancel_reservation(reservation_id):
    """Test cancelling a reservation"""
    if not reservation_id:
        print("No reservation ID provided, skipping cancel test")
        return
    
    print(f"Testing reservation cancellation for ID {reservation_id}...")
    response = requests.put(f"{BASE_URL}/api/reservations/{reservation_id}/cancel")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def run_all_tests():
    """Run all API tests"""
    print("Starting API Tests for Reservation Microservice")
    print("=" * 60)
    
    try:
        # Test health check
        test_health_check()
        
        # Test table operations
        table_id = test_create_table()
        test_get_tables()
        
        # Test reservation operations
        reservation_id = test_create_reservation()
        test_check_availability()
        test_get_reservations()
        
        if reservation_id:
            test_update_reservation(reservation_id)
            test_cancel_reservation(reservation_id)
        
        print("All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to the reservation service.")
        print("Make sure the service is running on http://localhost:5001")
    except Exception as e:
        print(f"Error during testing: {str(e)}")

if __name__ == "__main__":
    run_all_tests()
