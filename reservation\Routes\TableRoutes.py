from flask import Blueprint, request, jsonify
from ..Controllers.TableController import TableController

table_bp = Blueprint('tables', __name__)

@table_bp.route('/', methods=['POST'])
def create_table():
    """Create a new table"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    result, status_code = TableController.create_table(data)
    return jsonify(result), status_code

@table_bp.route('/', methods=['GET'])
def get_tables():
    """Get all tables"""
    result, status_code = TableController.get_tables()
    return jsonify(result), status_code

@table_bp.route('/<int:table_id>', methods=['GET'])
def get_table(table_id):
    """Get a specific table"""
    result, status_code = TableController.get_table(table_id)
    return jsonify(result), status_code

@table_bp.route('/<int:table_id>', methods=['PUT'])
def update_table(table_id):
    """Update a table"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    result, status_code = TableController.update_table(table_id, data)
    return jsonify(result), status_code

@table_bp.route('/<int:table_id>', methods=['DELETE'])
def delete_table(table_id):
    """Delete a table"""
    result, status_code = TableController.delete_table(table_id)
    return jsonify(result), status_code

@table_bp.route('/available', methods=['GET'])
def get_available_tables():
    """Get available tables with optional filters"""
    filters = {}
    
    # Extract query parameters
    if request.args.get('capacity'):
        try:
            filters['capacity'] = int(request.args.get('capacity'))
        except ValueError:
            return jsonify({'error': 'capacity must be a valid number'}), 400
    
    if request.args.get('location'):
        filters['location'] = request.args.get('location')
    
    result, status_code = TableController.get_available_tables(filters if filters else None)
    return jsonify(result), status_code
