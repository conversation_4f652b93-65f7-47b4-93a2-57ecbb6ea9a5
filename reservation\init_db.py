#!/usr/bin/env python3
"""
Database initialization script for the Reservation Microservice.
This script creates the database tables and optionally seeds initial data.
"""

import os
import sys

# Add the current directory to Python path to handle imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import create_app, db

def init_database(seed_tables=True):
    """Initialize the database with tables and optional seed data"""
    
    print("Initializing Reservation Microservice Database...")
    
    # Create Flask app
    app = create_app('development')
    
    with app.app_context():
        try:
            # Drop all tables (use with caution!)
            print("Dropping existing tables...")
            db.drop_all()
            
            # Create all tables
            print("Creating database tables...")
            db.create_all()
            
            # Verify tables were created
            tables_created = db.engine.table_names()
            print(f"Created tables: {tables_created}")
            
            if seed_tables:
                print("Seeding initial table data...")
                seed_initial_tables()
            
            print("Database initialization completed successfully!")
            
        except Exception as e:
            print(f"Error during database initialization: {str(e)}")
            sys.exit(1)

def seed_initial_tables():
    """Seed the database with initial table data"""
    from Models.Tables.TableModel import Table

    # Check if tables already exist
    if Table.query.count() > 0:
        print("Tables already exist. Skipping seed.")
        return
    
    # Restaurant table configuration
    tables_data = [
        # Window tables (2-person)
        {'table_number': 'W01', 'capacity': 2, 'location': 'window'},
        {'table_number': 'W02', 'capacity': 2, 'location': 'window'},
        {'table_number': 'W03', 'capacity': 2, 'location': 'window'},
        {'table_number': 'W04', 'capacity': 2, 'location': 'window'},
        
        # Center tables (4-person)
        {'table_number': 'C01', 'capacity': 4, 'location': 'center'},
        {'table_number': 'C02', 'capacity': 4, 'location': 'center'},
        {'table_number': 'C03', 'capacity': 4, 'location': 'center'},
        {'table_number': 'C04', 'capacity': 4, 'location': 'center'},
        {'table_number': 'C05', 'capacity': 4, 'location': 'center'},
        {'table_number': 'C06', 'capacity': 4, 'location': 'center'},
        
        # Large center tables (6-person)
        {'table_number': 'L01', 'capacity': 6, 'location': 'center'},
        {'table_number': 'L02', 'capacity': 6, 'location': 'center'},
        {'table_number': 'L03', 'capacity': 6, 'location': 'center'},
        
        # Extra large tables (8-person)
        {'table_number': 'XL01', 'capacity': 8, 'location': 'center'},
        {'table_number': 'XL02', 'capacity': 8, 'location': 'center'},
        
        # Patio tables
        {'table_number': 'P01', 'capacity': 2, 'location': 'patio'},
        {'table_number': 'P02', 'capacity': 2, 'location': 'patio'},
        {'table_number': 'P03', 'capacity': 4, 'location': 'patio'},
        {'table_number': 'P04', 'capacity': 4, 'location': 'patio'},
        {'table_number': 'P05', 'capacity': 6, 'location': 'patio'},
        
        # Bar seating
        {'table_number': 'B01', 'capacity': 2, 'location': 'bar'},
        {'table_number': 'B02', 'capacity': 2, 'location': 'bar'},
        {'table_number': 'B03', 'capacity': 4, 'location': 'bar'},
        
        # Private dining
        {'table_number': 'PR01', 'capacity': 10, 'location': 'private'},
        {'table_number': 'PR02', 'capacity': 12, 'location': 'private'},
    ]
    
    # Create table records
    for table_data in tables_data:
        table = Table(
            table_number=table_data['table_number'],
            capacity=table_data['capacity'],
            location=table_data['location'],
            is_available=True
        )
        db.session.add(table)
    
    # Commit all changes
    db.session.commit()
    
    print(f"Successfully seeded {len(tables_data)} tables:")
    print(f"  - Window tables: {len([t for t in tables_data if t['location'] == 'window'])}")
    print(f"  - Center tables: {len([t for t in tables_data if t['location'] == 'center'])}")
    print(f"  - Patio tables: {len([t for t in tables_data if t['location'] == 'patio'])}")
    print(f"  - Bar tables: {len([t for t in tables_data if t['location'] == 'bar'])}")
    print(f"  - Private tables: {len([t for t in tables_data if t['location'] == 'private'])}")

def show_table_summary():
    """Display a summary of tables in the database"""
    from Models.Tables.TableModel import Table

    app = create_app('development')

    with app.app_context():
        tables = Table.query.order_by(Table.table_number).all()
        
        if not tables:
            print("No tables found in database.")
            return
        
        print(f"\nTable Summary ({len(tables)} total tables):")
        print("-" * 50)
        
        # Group by location
        locations = {}
        for table in tables:
            location = table.location or 'unspecified'
            if location not in locations:
                locations[location] = []
            locations[location].append(table)
        
        for location, location_tables in locations.items():
            print(f"\n{location.upper()} TABLES:")
            for table in location_tables:
                status = "Available" if table.is_available else "Unavailable"
                print(f"  {table.table_number}: {table.capacity} seats - {status}")

def main():
    """Main function to handle command line arguments"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Initialize Reservation Database')
    parser.add_argument('--no-seed', action='store_true', 
                       help='Skip seeding initial table data')
    parser.add_argument('--show-tables', action='store_true',
                       help='Show table summary after initialization')
    
    args = parser.parse_args()
    
    # Initialize database
    init_database(seed_tables=not args.no_seed)
    
    # Show table summary if requested
    if args.show_tables:
        show_table_summary()

if __name__ == '__main__':
    main()
